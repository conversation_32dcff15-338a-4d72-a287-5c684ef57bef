Based on your query, you want to implement authentication for MCP servers using Composio's Python SDK, specifically creating integrations, managing entity IDs for users, and handling OAuth connections. Let me break down how to accomplish this using the Composio system.

## Answer

You can implement MCP server authentication with Composio using a three-step process: creating integrations, managing user entities, and handling OAuth connections.

### Step 1: Create Integrations for Your Apps

We will define a constants file which will include all the integration ids like so

notion:04a8a7a5-b159-4f16-82be-4c4b786b3689

### Step 2: Create Entity IDs for Users

Composio uses entity IDs to represent individual users in your application. [3](#0-2)

## Identifying Your User: The `entity_id`

A fundamental concept when creating Connections is the `entity_id`.

- **What:** A unique ID that represents _your_ end-user within Composio. Can map to DB/UUID in your app.
- **Why:** It allows Composio to use the correct credentials for your end-user in multi-tenant scenarios.
- **Default:** Composio uses the default ID `"default"`. This is suitable only for single-user scripts, personal testing.

You will pass the `entity_id` when initiating the connection process using the SDK, typically by first getting an `Entity` object:

``python Python
from composio_openai import ComposioToolSet, Action, App

toolset = ComposioToolSet()
user_identifier_from_my_app = "user_7a9f3b_db_id" # Example

# Get the Composio Entity object for your user

entity = toolset.get_entity(id=user_identifier_from_my_app)

# Use this 'entity' object to initiate connections

````
```typescript TypeScript
import { OpenAIToolSet } from "composio-core";

const toolset = new OpenAIToolSet();
const userIdentifierFromMyApp = "user_7a9f3b_db_id"; // Example

// Get the Composio Entity object for your user
const entity = await toolset.getEntity(userIdentifierFromMyApp);
// Use this 'entity' object to initiate connections

````

</CodeGroup>

You can get an entity object for a user like this: [4](#0-3)

### Step 3: Handle OAuth Connection Flow

For OAuth apps, you'll initiate connections that return redirect URLs for user authentication. [5](#0-4)

The OAuth flow involves redirecting users to authenticate: [6](#0-5)

And waiting for connection activation: [7](#0-6)

``python Python
from composio_openai import ComposioToolSet, Action, App

toolset = ComposioToolSet()
user_identifier_from_my_app = "user_7a9f3b_db_id" # Example

# Get the Composio Entity object for your user

entity = toolset.get_entity(id=user_identifier_from_my_app)

# Use this 'entity' object to initiate connections

````
```typescript TypeScript
import { OpenAIToolSet } from "composio-core";

const toolset = new OpenAIToolSet();
const userIdentifierFromMyApp = "user_7a9f3b_db_id"; // Example

// Get the Composio Entity object for your user
const entity = await toolset.getEntity(userIdentifierFromMyApp);
// Use this 'entity' object to initiate connections

````

</CodeGroup>

Your application needs to wait for this confirmation. Use the `wait_until_active` (Python) / `waitUntilActive` (TypeScript) method on the `connection_request` object obtained in Step 1.

<CodeGroup>
```python Python
# Assuming 'connection_request' from Step 1

print("Waiting for user authorization and connection activation...")
try: # Poll Composio until the status is ACTIVE
active_connection = connection_request.wait_until_active(
client=toolset.client, # Pass the Composio client instance
timeout=180 # Wait up to 3 minutes (adjust as needed)
)
print(f"Success! Connection is ACTIVE. ID: {active_connection.id}") # Store active_connection.id associated with your user (entity_id) # Now ready for Step 4.
except Exception as e: # Catches TimeoutError, etc.
print(f"Connection did not become active within timeout or failed: {e}") # Implement retry logic or inform the user

````
```typescript TypeScript
// Assuming 'connectionRequest' from Step 1
console.log("Waiting for user authorization and connection activation...");
try {
    // Poll Composio until the status is ACTIVE
    const activeConnection = await connectionRequest.waitUntilActive(180); // Wait up to 3 minutes

    console.log(`Success! Connection is ACTIVE. ID: ${activeConnection.id}`);
    // Store activeConnection.id associated with your user (entityId)
    // Now ready for Step 4.
} catch (error) {
    console.error("Connection did not become active within timeout or failed:", error);
    // Implement retry logic or inform the user
}
````

</CodeGroup>

### Step 2: Redirect the User

Your application **must** now direct the user's browser to the `redirectUrl` obtained in Step 1.

- **How:** This typically involves sending an HTTP 302 Redirect response from your backend, or using `window.location.href = redirectUrl;` in your frontend JavaScript.

The user will see the external service's login page (if not already logged in) followed by an authorization screen asking them to grant the permissions (scopes) defined in your Composio Integration.

### Step 4: Create MCP Servers with Authentication

Once you have integrations and user connections set up, you can create MCP servers that use these authenticated connections. [8](#0-7)

The MCP server URL can include user-specific parameters: [9](#0-8)

### Complete Implementation Flow

Here's how the pieces connect together:

1. **Integration Setup**: Use `toolset.create_integration()` or the CLI to create app integrations
2. **User Management**: Create entity IDs for each user using `toolset.get_entity(id=user_id)`
3. **OAuth Flow**: Use `entity.initiate_connection()` to get redirect URLs for user authentication
4. **MCP Server Creation**: Create MCP servers via API that reference your integrations
5. **Runtime Authentication**: MCP server URLs can include `user_id` parameters to bind sessions to specific authenticated users

The authentication flow is handled by Composio's backend, which manages OAuth tokens, refresh cycles, and connection states automatically. [10](#0-9)

## Notes

The Composio system provides a comprehensive authentication framework that handles the complexity of OAuth flows, token management, and multi-tenant user authentication for MCP servers. The integration between the Python SDK and MCP servers allows you to create authenticated tool access without managing credentials directly.

Wiki pages you might want to explore:

- [Authentication & Integration (ComposioHQ/composio)](/wiki/ComposioHQ/composio#5)
- [Documentation System (ComposioHQ/composio)](/wiki/ComposioHQ/composio#7.2)

<Step title="Create and configure an MCP server">
    Making the following request:
    ```bash
    curl -X POST https://backend.composio.dev/api/v3/mcp/servers \
      -H "x-api-key: <YOUR_API_KEY>" \
      -H "Content-Type: application/json" \
      -d '{
        "name": "Gmail",
        "apps": [
          "gmail"
        ],
        "auth_config_id": {}
      }'
    ```
    The response looks like:

    ```json
    {
      "id": "5bc757cc-7a8e-431c-8616-7f57cbed2423",
      "name": "gdrive_searcher",
      "auth_config_id": "a8a244c4-4a52-488e-8a01-2e504d069d16",
      "allowed_tools": [
        "GOOGLEDRIVE_FIND_FILE",
        "GOOGLEDRIVE_FIND_FOLDER",
        "GOOGLEDRIVE_DOWNLOAD_FILE"
      ],
      "mcp_url": "https://mcp.composio.dev/composio/server/5bc757cc-7a8e-431c-8616-7f57cbed2423?transport=sse",
      "commands": {
        "cursor": "npx @composio/mcp@latest setup \"<mcp_url>\" --client cursor",
        "claude":  "npx @composio/mcp@latest setup \"<mcp_url>\" --client claude",
        "windsurf": "npx @composio/mcp@latest setup \"<mcp_url>\" --client windsurf"
      },
      "created_at": "2025-05-18T22:15:25.926Z",
      "updated_at": "2025-05-18T22:15:25.926Z"
    }
    ```

  </Step>
